<script setup lang="ts">
import { Document } from "flexsearch";
import { useEditor, EditorContent } from "@tiptap/vue-3";
import { StarterKit } from "@tiptap/starter-kit";
import { Node, mergeAttributes } from "@tiptap/core";
import { Suggestion } from "@tiptap/suggestion";
import { PluginKey } from "@tiptap/pm/state";
import { ref, computed, onMounted, onUnmounted, watch } from "vue";
import { useI18n } from "vue-i18n";
import { getFlexSearchOptions, searchOptios } from "@/modules/sentire/config";
import { useSentireStore } from "@/modules/sentire/stores";
import { storeToRefs } from "pinia";
import { filter, forEach } from "lodash-es";
import { Button } from "@/components/ui/button";
// import {
//   Tooltip,
//   TooltipContent,
//   TooltipTrigger,
// } from "@/components/ui/tooltip";
// AtomIcon as Atom,
import {
  HourglassIcon,
  MonitorIcon,
  ServerOffIcon,
  TextSearchIcon,
  ArrowUpWideNarrowIcon,
  Columns2Icon,
  ArrowUpIcon,
  MapPinIcon as LocalTwo,
  NetworkIcon as ConnectionPoint,
  XIcon as Close,
  AudioLinesIcon as Acoustic,
  SquareIcon as Square,
  LayoutGridIcon as AllApplication,
} from "lucide-vue-next";
import { useCurrentThreadStore } from "~/modules/sentire/stores/currentThreadStore";
import type { UserInput } from "../types";
import VoiceInput, { type VoiceInputResult } from "./VoiceInput.vue";

interface FlexSearchDocument {
  [key: string]: string;
  content: string;
  type: string;
}

interface CompletionItem {
  id: string;
  label: string;
  type: "app" | "site" | "link";
}

interface SearchResults {
  apps: CompletionItem[];
  sites: CompletionItem[];
  links: CompletionItem[];
}

interface SearchResult {
  doc: FlexSearchDocument;
}

const audioConfig = {
  stt: { engine: "web" },
};

const { t, locale } = useI18n();
const sentireStore = useSentireStore();
const {
  allApps: apps,
  allSites: sites,
  allLinks: links,
} = storeToRefs(sentireStore);

const { simple } = defineProps<{
  simple?: boolean;
}>();

const emit = defineEmits<{
  (e: "search", value: UserInput): void;
}>();

const currentThreadStore = useCurrentThreadStore();
const { isRunning } = storeToRefs(currentThreadStore);

const searchIndex = ref<Document<FlexSearchDocument> | null>(null);
const completionItems = ref<CompletionItem[]>([]);
const selectedIndex = ref(0);
const showSuggestions = ref(false);
const suggestionPosition = ref({ top: 0, left: 0 });
const suggestionListRef = ref<HTMLElement | null>(null);
const isFocused = ref(false);
const isSearch = ref(false);
const showSearchTip = ref(true);
const activeIndex = ref(-1);
const recording = ref(false);

// 计算 # 字符的位置
const calculateSuggestionPosition = () => {
  if (!editor.value) return;

  try {
    const editorElement = editor.value.view.dom;
    const selection = editor.value.state.selection;
    const content = editor.value.getText();

    // 找到最后一个 # 字符的位置
    const lastHashIndex = content.lastIndexOf("#");

    // 获取建议框高度，未渲染时默认 40px
    const suggestionBoxHeight = suggestionListRef.value?.offsetHeight || 200;
    if (lastHashIndex === -1) {
      // 如果没有找到 #，使用当前光标位置
      const coords = editor.value.view.coordsAtPos(selection.from);
      const editorRect = editorElement.getBoundingClientRect();

      suggestionPosition.value = {
        top: simple
          ? coords.top - editorRect.top - suggestionBoxHeight
          : coords.top - editorRect.top + 24,
        left: coords.left - editorRect.left,
      };
    } else {
      // 使用 # 字符的位置
      const coords = editor.value.view.coordsAtPos(lastHashIndex + 1); // +1 为了定位到 # 后面
      const editorRect = editorElement.getBoundingClientRect();

      suggestionPosition.value = {
        top: simple
          ? coords.top - editorRect.top - suggestionBoxHeight
          : coords.top - editorRect.top + 24,
        left: coords.left - editorRect.left,
      };
    }
  } catch (error) {
    console.error("Error calculating suggestion position:", error);
    // 回退到相对安全的位置
    suggestionPosition.value = { top: 30, left: 0 };
  }
};

// 滚动到当前选中的项目
const scrollToSelectedItem = () => {
  if (!suggestionListRef.value) return;

  const selectedItem = suggestionListRef.value.children[
    selectedIndex.value
  ] as HTMLElement;
  if (!selectedItem) return;

  const container = suggestionListRef.value;
  const itemTop = selectedItem.offsetTop;
  const itemHeight = selectedItem.offsetHeight;
  const containerHeight = container.offsetHeight;
  const scrollTop = container.scrollTop;

  // 检查当前选中的项目是否在可视区域内
  const isVisible =
    itemTop >= scrollTop && itemTop + itemHeight <= scrollTop + containerHeight;

  if (!isVisible) {
    // 滚动到选中项目
    container.scrollTop = itemTop - containerHeight / 2 + itemHeight / 2;
  }
};

// 构建搜索索引
const buildIndex = () => {
  if (!searchIndex.value) return;

  forEach(apps.value, (doc, index) => {
    searchIndex.value?.add(index, {
      content: doc,
      type: "app",
    });
  });

  forEach(sites.value, (doc, index) => {
    searchIndex.value?.add(index + (apps.value.length || 0), {
      content: doc,
      type: "site",
    });
  });

  forEach(links.value, (doc, index) => {
    searchIndex.value?.add(
      index + (apps.value.length || 0) + (sites.value.length || 0),
      {
        content: doc,
        type: "link",
      }
    );
  });
};

const initFlexSearch = () => {
  const options = getFlexSearchOptions(locale.value);
  searchIndex.value = new Document<FlexSearchDocument>(options as any);
  buildIndex();
};

// 搜索功能
const search = (searchQuery: string): SearchResults => {
  if (!searchIndex.value) return { apps: [], sites: [], links: [] };

  const searchResults = searchIndex.value.searchCache(searchQuery, {
    ...searchOptios,
    limit: 5,
  }) as SearchResult[];

  const apps = filter(
    searchResults,
    (item: SearchResult) => item.doc.type === "app"
  ).map((item: SearchResult) => ({
    id: `app-${item.doc.content}`,
    label: item.doc.content,
    type: "app" as const,
  }));

  const sites = filter(
    searchResults,
    (item: SearchResult) => item.doc.type === "site"
  ).map((item: SearchResult) => ({
    id: `site-${item.doc.content}`,
    label: item.doc.content,
    type: "site" as const,
  }));

  const links = filter(
    searchResults,
    (item: SearchResult) => item.doc.type === "link"
  ).map((item: SearchResult) => ({
    id: `link-${item.doc.content}`,
    label: item.doc.content,
    type: "link" as const,
  }));

  return { apps, sites, links };
};

// 存储当前的 suggestion command
let currentSuggestionCommand: ((item: CompletionItem) => void) | null = null;

// 处理点击选择项目
const handleItemClick = (item: CompletionItem) => {
  if (currentSuggestionCommand) {
    currentSuggestionCommand(item);
  }
};

// 创建自定义 Mention 节点
const MentionNode = Node.create({
  name: "mention",
  group: "inline",
  inline: true,
  selectable: false,
  atom: true,

  addAttributes() {
    return {
      id: {
        default: null,
        parseHTML: (element: HTMLElement) => element.getAttribute("data-id"),
        renderHTML: (attributes) => {
          if (!attributes.id) return {};
          return { "data-id": attributes.id };
        },
      },
      label: {
        default: null,
        parseHTML: (element: HTMLElement) => element.getAttribute("data-label"),
        renderHTML: (attributes) => {
          if (!attributes.label) return {};
          return { "data-label": attributes.label };
        },
      },
      type: {
        default: null,
        parseHTML: (element: HTMLElement) => element.getAttribute("data-type"),
        renderHTML: (attributes) => {
          if (!attributes.type) return {};
          return { "data-type": attributes.type };
        },
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: "span[data-type='mention']",
      },
    ];
  },

  renderHTML({ HTMLAttributes, node }) {
    return [
      "span",
      mergeAttributes(
        {
          "data-type": "mention",
          class: "mention text-sm",
        },
        HTMLAttributes
      ),
      `${node.attrs.label || HTMLAttributes["data-label"] || "unknown"}`,
    ];
  },

  addProseMirrorPlugins() {
    return [
      Suggestion({
        pluginKey: new PluginKey("mention"),
        editor: this.editor,
        char: "#",
        startOfLine: false,

        items: ({ query }: { query: string }) => {
          if (!query) {
            // 当没有查询内容时，显示所有可用选项（限制数量）
            const allApps = apps.value.slice(0, 3).map((app) => ({
              id: `app-${app}`,
              label: app,
              type: "app" as const,
            }));
            const allSites = sites.value.slice(0, 3).map((site) => ({
              id: `site-${site}`,
              label: site,
              type: "site" as const,
            }));
            const allLinks = links.value.slice(0, 3).map((link) => ({
              id: `link-${link}`,
              label: link,
              type: "link" as const,
            }));

            const allItems = [...allApps, ...allSites, ...allLinks];
            completionItems.value = allItems;
            return allItems;
          }

          const results = search(query);
          const allItems = [
            ...results.apps,
            ...results.sites,
            ...results.links,
          ];
          completionItems.value = allItems;
          return allItems;
        },

        command: ({ editor, range, props }) => {
          // 使用 command 来处理选择，这样可以正确处理 range
          const item = props as CompletionItem;

          editor
            .chain()
            .focus()
            .deleteRange(range)
            .insertContentAt(range.from, [
              {
                type: "mention",
                attrs: {
                  id: item.id,
                  label: item.label,
                  type: item.type,
                },
              },
              {
                type: "text",
                text: " ",
              },
            ])
            .run();

          showSuggestions.value = false;
        },

        render: () => {
          return {
            onStart: (props) => {
              showSuggestions.value = true;
              selectedIndex.value = 0;
              currentSuggestionCommand = props.command;

              // 计算提示框位置
              setTimeout(() => {
                calculateSuggestionPosition();
              }, 0);
            },

            onUpdate: (props) => {
              completionItems.value = props.items;
              selectedIndex.value = 0;
              currentSuggestionCommand = props.command;

              // 更新提示框位置
              setTimeout(() => {
                calculateSuggestionPosition();
              }, 0);
            },

            onExit: () => {
              showSuggestions.value = false;
              currentSuggestionCommand = null;
            },

            onKeyDown: () => {
              return false;
            },
          };
        },
      }),
    ];
  },
});

// 创建编辑器
const editor = useEditor({
  content: "",
  extensions: [
    StarterKit.configure({
      bold: false,
      italic: false,
      bulletList: false,
      orderedList: false,
      heading: false,
      listItem: false,
      strike: false,
      code: false,
      codeBlock: false,
    }),
    MentionNode,
  ],
  editorProps: {
    attributes: {
      class: "w-full min-h-[48px] outline-none",
    },
    handleKeyDown: (view, event) => {
      // 如果建议框显示，阻止某些按键的默认行为
      if (showSuggestions.value) {
        if (
          ["Enter", "Tab", "ArrowUp", "ArrowDown", "Escape"].includes(event.key)
        ) {
          return true; // 阻止默认行为
        }

        // Ctrl+K 或 Ctrl+J 也需要阻止
        if (event.ctrlKey && (event.key === "k" || event.key === "j")) {
          return true;
        }
      }

      // 当建议框没有显示时，Enter 键触发搜索
      if (event.key === "Enter" && !showSuggestions.value) {
        if (isRunning.value) {
          return true;
        }
        event.preventDefault();
        handleSearch();
        return true;
      }

      // 对于其他情况，让默认处理器处理
      return false;
    },
  },
  onFocus: () => {
    isFocused.value = true;
  },
  onBlur: () => {
    isFocused.value = false;
  },
});

// 是否为空
const isEmpty = computed(() => {
  if (!editor.value) return true;

  // 使用 TipTap 的 isEmpty 属性，它能正确处理各种节点类型
  return editor.value.isEmpty;
});

const getUserInput = () => {
  if (!editor.value) {
    return {
      question: "",
      entities: [],
    };
  }

  const userInput: UserInput = { question: "", entities: [] };
  editor.value.state.doc.descendants((node) => {
    if (node.type.name === "mention") {
      userInput.entities.push({
        type: node.attrs.type,
        label: node.attrs.label,
      });
      userInput.question += node.attrs.label;
    } else if (node.type.name === "text") {
      userInput.question += node.text;
    }
  });

  return userInput;
};

// 处理搜索
const handleSearch = () => {
  if (editor.value && !isEmpty.value) {
    emit("search", getUserInput());
    showSearchTip.value = false;
    try {
      editor.value.commands.clearContent();
    } catch (error) {
      console.error("Error clearing content:", error);
    } finally {
      showSearchTip.value = true;
    }
  }
};

// 清空内容
const handleClear = () => {
  editor.value?.commands.clearContent();
  editor.value?.commands.focus();
  activeIndex.value = -1;
};

// 监听数据变化重新构建索引
watch([apps, sites, links], () => {
  if (searchIndex.value) {
    buildIndex();
  }
});

// 添加全局键盘事件处理
const handleGlobalKeydown = (event: KeyboardEvent) => {
  if (!showSuggestions.value || completionItems.value.length === 0) return;

  // 阻止默认行为
  if (["ArrowUp", "ArrowDown", "Enter", "Tab", "Escape"].includes(event.key)) {
    event.preventDefault();
    event.stopPropagation();
  }

  // Ctrl+K 或 Ctrl+J
  if (event.ctrlKey && (event.key === "k" || event.key === "j")) {
    event.preventDefault();
    event.stopPropagation();
  }

  if (event.key === "Escape") {
    showSuggestions.value = false;
    return;
  }

  // Ctrl+K: 向上移动
  if (event.ctrlKey && event.key === "k") {
    selectedIndex.value = Math.max(0, selectedIndex.value - 1);
    setTimeout(() => scrollToSelectedItem(), 0);
    return;
  }

  // Ctrl+J: 向下移动
  if (event.ctrlKey && event.key === "j") {
    selectedIndex.value = Math.min(
      completionItems.value.length - 1,
      selectedIndex.value + 1
    );
    setTimeout(() => scrollToSelectedItem(), 0);
    return;
  }

  if (event.key === "ArrowUp") {
    selectedIndex.value = Math.max(0, selectedIndex.value - 1);
    setTimeout(() => scrollToSelectedItem(), 0);
    return;
  }

  if (event.key === "ArrowDown") {
    selectedIndex.value = Math.min(
      completionItems.value.length - 1,
      selectedIndex.value + 1
    );
    setTimeout(() => scrollToSelectedItem(), 0);
    return;
  }

  if (event.key === "Enter" || event.key === "Tab") {
    const item = completionItems.value[selectedIndex.value];
    if (item && currentSuggestionCommand) {
      currentSuggestionCommand(item);
    }
    return;
  }
};

const actions = [
  { label: "Slow performance", icon: HourglassIcon },
  { label: "Connection failures", icon: ServerOffIcon },
  { label: "Monitor", icon: MonitorIcon },
  { label: "Traffic overview", icon: TextSearchIcon },
  { label: "Traffic composition", icon: ArrowUpWideNarrowIcon },
  { label: "Compare", icon: Columns2Icon },
];

//修改传入文字
const handleActionClick = (index: number) => {
  activeIndex.value = index;
  switch (index) {
    case 0:
      setContent("show performance issues");
      break;
    case 1:
      setContent("Connection failures");
      break;
    case 2:
      setContent("Real-time monitoring");
      break;
    case 3:
      setContent("traffic overview");

      break;
    case 4:
      setContent("Traffic composition");
      break;
    case 5:
      setContent("Compare");
      break;
    default:
      break;
  }
};

const setContent = (content: string) => {
  if (editor.value) {
    editor.value.commands.clearContent();
    editor.value.commands.insertContent(content);
  }
};

const handleVoiceInput = () => {
  recording.value = true;
};

const handleVoiceInputConfirm = (data: VoiceInputResult) => {
  recording.value = false;
  setContent(data.text || "");
};

const handleVoiceInputCancel = () => {
  recording.value = false;
};

onMounted(() => {
  initFlexSearch();

  // 添加全局键盘事件监听
  document.addEventListener("keydown", handleGlobalKeydown);

  if (!isRunning) {
    editor.value?.commands.focus();
  }
});

// 在组件卸载时移除监听器
onUnmounted(() => {
  document.removeEventListener("keydown", handleGlobalKeydown);
});

defineExpose({
  setContent,
});
</script>

<template>
  <div
    class="flex-grow flex items-center gap-4 justify-center"
    :class="{ 'flex-col': !isSearch }"
  >
    <h1
      v-if="!isSearch && !simple"
      class="title text-3xl font-semibold leading-tight text-center mb-8 quickfocus-title mt-[calc(50vh-240px)]"
      style="color: #595959"
    >
      SENTIRE
    </h1>
    <div class="flex w-full items-start">
      <div
        id="search-bar"
        class="relative flex w-full flex-col justify-center items-center gap-4"
      >
        <div
          class="relative w-full rounded-2xl bg-white border border-[#bfbfbf] p-2 hover:border-[#3a7ca5] transition-colors duration-200 shadow-amber-600 shadow-custom"
          :class="{
            'is-focused': isFocused,
            '!max-w-[800px]': !isSearch && !simple,
            'max-w-[1054px]': !(!isSearch && !simple),
            'input-outer-disabled': isRunning,
          }"
        >
          <div class="relative min-h-12">
            <div
              class="w-full editor-container rounded-lg-new max-h-64 overflow-y-auto"
              :class="{ 'editor-container-disabled': isRunning }"
              :tabindex="isRunning ? -1 : 0"
            >
              <EditorContent
                :editor="editor"
                :data-placeholder="t('Describe your problem')"
                class="first:before:text-[#bfbfbf] first:before:float-left first:before:pointer-events-none first:before:h-0 w-full min-h-[48px] rounded-lg-new p-2"
                :class="{
                  'first:before:content-[attr(data-placeholder)]': isEmpty,
                }"
              />

              <!-- 自动完成提示框 - 使用 QuestionInput 的样式 -->
              <div
                v-show="showSuggestions && completionItems.length > 0"
                ref="suggestionListRef"
                class="completion-box"
                :style="{
                  top: suggestionPosition.top + 'px',
                  left: suggestionPosition.left + 'px',
                }"
              >
                <template
                  v-for="(item, index) in completionItems"
                  :key="item.id"
                >
                  <div
                    v-if="
                      index === 0 ||
                      completionItems[index - 1].type !== item.type
                    "
                    class="flex items-center gap-x-1"
                  >
                    <AllApplication v-if="item.type === 'app'" :size="18" />
                    <LocalTwo v-else-if="item.type === 'site'" :size="18" />
                    <ConnectionPoint
                      v-else-if="item.type === 'link'"
                      :size="18"
                    />
                    <span>{{
                      item.type === "app"
                        ? t("Application")
                        : item.type === "site"
                        ? t("Site")
                        : t("Link")
                    }}</span>
                  </div>
                  <div
                    class="completion-item text-sm"
                    :class="{ selected: index === selectedIndex }"
                    @click="handleItemClick(item)"
                  >
                    <span>{{ item.label }}</span>
                  </div>
                </template>
              </div>
            </div>

            <div v-if="recording" v-motion-fade-visible class="space-y-2 mt-2">
              <VoiceInput
                :recording="recording"
                :transcribe="true"
                :audio-config="audioConfig"
                @confirm="handleVoiceInputConfirm"
                @cancel="handleVoiceInputCancel"
              />
            </div>

            <div v-else class="flex justify-end items-center mt-3">
              <!-- Diagnostic Switch -->
              <!-- <div class="flex items-center gap-2">
                <Tooltip v-if="!isRunning">
                  <TooltipTrigger as-child>
                    <Button
                      :disabled="isRunning"
                      variant="ghost"
                      size="sm"
                      class="rounded-full text-default-text hover:text-default-text"
                      :class="[
                        enableDiagnostic
                          ? 'bg-[#e2ecf2] text-[#3a7ca5] border border-[#3c7ca34d] hover:text-[#3a7ca5] hover:bg-[#e2ecf2]'
                          : 'border border-[#e5e5e5]',
                      ]"
                      @click="
                        currentThreadStore.setEnableDiagnostic(
                          !enableDiagnostic
                        )
                      "
                    >
                      <Atom
                        theme="outline"
                        :size="16"
                        class="transition-all duration-300 hover:scale-110 flex items-center"
                        :class="enableDiagnostic ? 'atom-rotate' : 'atom-flip'"
                      />
                      <span class="text-sm"> Diagnostic </span>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent
                    class="!bg-[#333] !border-[#333]"
                    :hide-arrow="true"
                  >
                    <p>Force enable diagnostic mode</p>
                  </TooltipContent>
                </Tooltip>
              </div> -->
              <div class="flex gap-2 justify-center">
                <Button
                  v-if="!isEmpty && !isSearch && !isRunning"
                  color="gray"
                  variant="ghost"
                  size="sm"
                  class="w-8 h-8 text-[#bfbfbf] text-center flex items-center justify-center cursor-pointer hover:bg-[#d9f0f0] hover:text-[#3a7ca5] active:text-[#3a7ca5] transition-colors duration-200 active:bg-[transparent] rounded-full"
                  :style="{ visibility: isEmpty ? 'hidden' : 'visible' }"
                  @click="handleClear"
                >
                  <Close
                    theme="outline"
                    :size="16"
                    class="transition-all duration-300 hover:scale-110 flex items-center"
                  />
                </Button>
                <Button
                  v-if="!isRunning"
                  color="primary"
                  variant="ghost"
                  size="sm"
                  class="cursor-pointer w-8 h-8 flex items-center justify-center text-[#3a7ca5] hover:bg-[#d9f0f0] active:text-[#4e8888] transition-all duration-300 active:bg-[transparent] rounded-full"
                  :class="{
                    'is-active': isFocused,
                  }"
                  @click="handleVoiceInput()"
                >
                  <Acoustic
                    theme="outline"
                    :size="16"
                    class="transition-all duration-300 hover:scale-110 flex items-center"
                  />
                </Button>
                <Button
                  v-if="!isRunning"
                  color="primary"
                  size="sm"
                  class="send-button send-button-circular cursor-pointer w-8 h-8 flex items-center justify-center bg-[#bfbfbf] hover:bg-[#8fb2c8] active:bg-[#8fb2c8] transition-all duration-300"
                  :class="{
                    'is-active': isFocused,
                    'is-not-empty': !isEmpty,
                  }"
                  @click="handleSearch()"
                >
                  <ArrowUpIcon
                    theme="outline"
                    :size="16"
                    color="#ffffff"
                    class="transition-all duration-300 hover:scale-110 flex items-center"
                  />
                </Button>
                <Button
                  v-if="isRunning"
                  color="primary"
                  size="sm"
                  class="send-button send-button-circular cursor-pointer w-8 h-8 flex items-center justify-center bg-[#3a7ca5] transition-all duration-300"
                  @click="currentThreadStore.cancelAnswerUserQuestion()"
                >
                  <Square
                    theme="outline"
                    :size="16"
                    color="#ffffff"
                    class="transition-all duration-300 flex items-center"
                  />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    v-if="showSearchTip && !simple"
    class="search-bar-actions grid grid-cols-3 gap-x-6 gap-y-3 w-full mx-auto mt-6"
    :class="{ '!max-w-[800px]': showSearchTip }"
  >
    <Button
      v-for="(item, idx) in actions"
      :key="item.label"
      style="margin: 0px"
      variant="secondary"
      class="justify-center hover:bg-[#e2ecf2] hover:text-[#3a7ca5] cursor-pointer text-default-text"
      :class="{ 'text-[#3a7ca5] bg-[#e2ecf2]': idx === activeIndex }"
      @click="handleActionClick(idx)"
    >
      <component :is="item.icon" :size="16" class="mr-2 text-base" />
      <div class="text-sm font-medium leading-snug">{{ item.label }}</div>
    </Button>
  </div>
</template>

<style lang="scss" scoped>
$primary-color: #3a7ca5;
$secondary-color: #bfbfbf;
$hover-color: #dcecfa;

.title {
  color: $primary-color;
  font-weight: 900;
  font-family: "Roboto Mono", sans-serif;
}

.is-focused {
  border: 1px solid $primary-color;
}

.is-not-empty {
  background-color: $primary-color;
}
.is-active {
  color: $primary-color;
}

.send-button-circular {
  border-radius: var(--radius-full);
  min-width: 32px;
  min-height: 32px;

  /* 确保覆盖 Button 组件的默认圆角 */
  &:deep(.reka-button) {
    border-radius: var(--radius-full);
  }

  /* 使用更高的 CSS 特异性来覆盖默认样式 */
  &.send-button.send-button-circular {
    border-radius: var(--radius-full);
  }
}
.search-icon {
  width: 24px;
  height: 24px;
}
.quickfocus-title {
  font-family: "Sentire", sans-serif;
}



.editor-disabled {
  opacity: 0.6 !important;
  pointer-events: none !important;
  cursor: not-allowed !important;
}

/* .editor-container-disabled {
  background-color: #f5f5f5 !important;

  &:hover {
    border-color: #bfbfbf !important;
  }
} */

/* .input-outer-disabled {
  background-color: #f5f5f5 !important;
  border-color: #bfbfbf !important;
  transition: background-color 0.2s, border-color 0.2s;
} */

.completion-box {
  position: absolute;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
  padding: 8px;
  min-width: 200px;
}

.completion-item {
  padding: 8px 12px;
  cursor: pointer;
  border-radius: 0.25rem; /* 4px - 使用Tailwind sm值 */

  &:hover {
    background-color: #f3f4f6;
  }

  &.selected {
    background-color: #dbeafe;
    color: #1e40af;
  }
}

:deep(.ProseMirror) {
  outline: none !important;
  border: none !important;
  min-height: 48px;
  line-height: 1.5;

  .ProseMirror-trailingBreak {
    display: none;
  }
}

:deep(.ProseMirror > p) {
  min-height: 48px;
  margin: 0;
}

:deep(.ProseMirror[data-placeholder]:empty:before) {
  content: attr(data-placeholder);
  color: #bfbfbf;
  //pointer-events: none;
  float: left;
  height: 0;
}

:deep(.mention) {
  color: #3a7ca5;
  padding: 2px 6px;
  border-radius: 0.25rem; /* 4px - 使用Tailwind sm值 */
  font-weight: 500;
}

.atom-rotate {
  animation: atomRotate 0.2s ease-in-out;
}

.atom-flip {
  animation: atomFlip 0.2s ease-in-out;
}

@keyframes atomRotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(90deg);
  }
}

@keyframes atomFlip {
  0% {
    transform: rotateY(0deg);
  }
  50% {
    transform: rotateY(90deg);
  }
  100% {
    transform: rotateY(0deg);
  }
}
</style>
